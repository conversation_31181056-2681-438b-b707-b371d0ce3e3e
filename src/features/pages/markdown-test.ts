import { LitElement, html } from 'lit';
import { customElement } from 'lit/decorators.js';

// Import the demo components
import '@components/common/markdown-demo';
import '@components/common/contenteditable-test';

/**
 * Test page for markdown functionality
 * Navigate to /markdown-test to see the demo
 */
@customElement('markdown-test-page')
export class MarkdownTestPage extends LitElement {
  render() {
    return html`
      <div style="padding: 2rem;">
        <contenteditable-test></contenteditable-test>
        <hr style="margin: 3rem 0;">
        <markdown-demo></markdown-demo>
      </div>
    `;
  }
}
