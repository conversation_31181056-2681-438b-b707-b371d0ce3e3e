# Solutions ordered by the closest upcoming deadline.
# `now` is seconds since the epoch and used to filter Solutions where the deadline already passed.

query SolutionsByDeadline($first: Int = 4, $skip: Int = 0, $now: BigInt!) {
    solutions(
        first: $first,
        skip: $skip,
        where: { deadline_gt: $now },
        orderBy: deadline,
        orderDirection: asc
    ) {
        ...SolutionFieldsDetailed
    }
}