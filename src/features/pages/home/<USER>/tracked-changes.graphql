query TrackedChanges(
    $ideaIds: [String!]!
    $solutionIds: [Bytes!]!
  $since: BigInt!
    $now: BigInt!
) {
    # New supporters for the ideas
  newSupporters: ideaContributions(
    where: { idea_in: $ideaIds, createdTime_gt: $since }
    orderBy: createdTime
    orderDirection: desc
  ) {
    idea {
      id
      name
    }
    funder {
      id
      profile
    }
    createdTime
  }

    # New solutions for the ideas
  newSolutions: solutions(
    where: { idea_in: $ideaIds, startTime_gt: $since }
    orderBy: startTime
    orderDirection: desc
  ) {
      ...SolutionFieldsDetailed
  }

    # Updated solutions
  solutionUpdated: solutions(
    where: { id_in: $solutionIds, modifiedTime_gt: $since }
    orderBy: modifiedTime
    orderDirection: desc
  ) {
    ...SolutionFields
  }

    # Solutions that passed their deadline
    deadlinePassed: solutions(
        where: { id_in: $solutionIds, deadline_lt: $now, deadline_gt: $since }
        orderBy: deadline
        orderDirection: desc
    ) {
        ...SolutionFields
    }

    # New funders for solutions
  newFunders: solutionContributions(
    where: { solution_: { id_in: $solutionIds }, createdTime_gt: $since }
    orderBy: createdTime
    orderDirection: desc
  ) {
    solution {
      ...SolutionFields
    }
    funder {
      id
      profile
    }
    createdTime
  }
}
