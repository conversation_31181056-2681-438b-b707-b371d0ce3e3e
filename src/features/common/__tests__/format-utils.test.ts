import { expect } from '@open-wc/testing';
import { formattedText } from '@utils/format-utils';

describe('formattedText', () => {
  describe('HTML input', () => {
    it('should preserve allowed HTML formatting tags', () => {
      const input = '<p>Hello <strong>world</strong> with <em>emphasis</em>!</p>';
      const result = formattedText(input);
      
      // The result is a Lit directive, so we need to check its value
      expect(result.strings).to.exist;
      expect(result.values).to.exist;
      expect(result.values[0]).to.equal('<p>Hello <strong>world</strong> with <em>emphasis</em>!</p>');
    });

    it('should remove dangerous script tags', () => {
      const input = '<p>Hello <script>alert("xss")</script> world!</p>';
      const result = formattedText(input);
      
      expect(result.values[0]).to.equal('<p>Hello  world!</p>');
    });

    it('should preserve links with href attributes', () => {
      const input = '<p>Visit <a href="https://example.com">our website</a></p>';
      const result = formattedText(input);
      
      expect(result.values[0]).to.equal('<p>Visit <a href="https://example.com">our website</a></p>');
    });
  });

  describe('Markdown input', () => {
    it('should convert markdown headers to HTML', () => {
      const input = '# Main Title\n\n## Subtitle';
      const result = formattedText(input);
      
      expect(result.values[0]).to.include('<h1>Main Title</h1>');
      expect(result.values[0]).to.include('<h2>Subtitle</h2>');
    });

    it('should convert markdown bold and italic to HTML', () => {
      const input = 'This is **bold** and *italic* text.';
      const result = formattedText(input);
      
      expect(result.values[0]).to.include('<strong>bold</strong>');
      expect(result.values[0]).to.include('<em>italic</em>');
    });

    it('should convert markdown links to HTML', () => {
      const input = 'Check out [our website](https://example.com) for more info.';
      const result = formattedText(input);
      
      expect(result.values[0]).to.include('<a href="https://example.com">our website</a>');
    });

    it('should convert markdown lists to HTML', () => {
      const input = '- Item 1\n- Item 2\n- Item 3';
      const result = formattedText(input);
      
      expect(result.values[0]).to.include('<ul>');
      expect(result.values[0]).to.include('<li>Item 1</li>');
      expect(result.values[0]).to.include('<li>Item 2</li>');
      expect(result.values[0]).to.include('<li>Item 3</li>');
    });

    it('should convert markdown blockquotes to HTML', () => {
      const input = '> This is a blockquote\n> with multiple lines';
      const result = formattedText(input);
      
      expect(result.values[0]).to.include('<blockquote>');
    });

    it('should convert markdown code blocks to HTML', () => {
      const input = '```javascript\nconsole.log("Hello World");\n```';
      const result = formattedText(input);
      
      // Note: marked might wrap code in <pre><code> tags
      expect(result.values[0]).to.include('console.log');
    });
  });

  describe('Plain text input', () => {
    it('should handle plain text without markdown or HTML', () => {
      const input = 'This is just plain text with no special formatting.';
      const result = formattedText(input);
      
      // Plain text should be wrapped in a paragraph by marked
      expect(result.values[0]).to.include('This is just plain text');
    });
  });

  describe('Mixed content', () => {
    it('should handle markdown with HTML entities', () => {
      const input = '**Bold text** with &amp; entities';
      const result = formattedText(input);
      
      expect(result.values[0]).to.include('<strong>Bold text</strong>');
      expect(result.values[0]).to.include('&amp;');
    });

    it('should sanitize dangerous HTML even when converted from markdown', () => {
      // This tests that even if markdown somehow produces dangerous HTML, it gets sanitized
      const input = 'Normal **bold** text';
      const result = formattedText(input);
      
      expect(result.values[0]).to.include('<strong>bold</strong>');
      expect(result.values[0]).not.to.include('<script>');
    });
  });
});
