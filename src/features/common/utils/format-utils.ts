import { unsafeHTML } from 'lit/directives/unsafe-html.js';
import { DirectiveResult } from 'lit/directive.js';

import { formatUnits } from 'viem';
import DOMPurify, { Config } from 'dompurify';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { marked } from 'marked';
import TurndownService from 'turndown';

dayjs.extend(relativeTime);

import { updraftSettings } from '@state/common';

/**
 * Regular expression pattern for validating Ethereum addresses
 * Matches a 0x prefix followed by exactly 40 hexadecimal characters
 */
export const ethAddressPattern = /^0x[a-fA-F0-9]{40}$/;

/**
 * Shortens an Ethereum address for display purposes
 * @param address The full Ethereum address
 * @returns A shortened version of the address (e.g., 0x1234...5678)
 */
export function shortenAddress(address: string): string {
  if (!address) return '';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

/**
 * Formats a funder reward percentage from the raw value
 * @param funderReward The raw funder reward value
 * @returns Formatted percentage string with 0 decimal places
 */
export function formatReward(funderReward: number): string {
  const pctFunderReward =
    (funderReward * 100) / updraftSettings.get().percentScale;
  return `${pctFunderReward.toFixed(0)}%`;
}

/**
 * Formats a token amount for display
 * @param amount The token amount as a bigint
 * @returns Formatted token amount string
 */
export function formatAmount(amount: bigint): string {
  if (!amount) return '0';
  return shortNum(formatUnits(amount, 18));
}

/**
 * Formats a date for display in a consistent way
 * @param timestamp Unix timestamp in seconds
 * @param format The format to use (fromNow, formatted, full)
 * @returns formatted date string
 */
export function formatDate(timestamp: number, format: string) {
  const date = dayjs(timestamp * 1000);
  switch (format) {
    case 'fromNow':
      return date.fromNow();
    case 'withTime':
      return date.format('MMM D, YYYY [at] h:mm A');
    case 'full':
      return `${date.format('MMM D, YYYY [at] h:mm A')} (${date.fromNow()})`;
    default:
      return date.format('MMM D, YYYY');
  }
}

/**
 * Capitalizes the first letter of a string
 * @param s The string to capitalize
 * @returns The capitalized string
 */
export function capitalize(s: string): string {
  return s.charAt(0).toUpperCase() + s.slice(1);
}

/**
 * Formats a number with appropriate suffixes (K, M, B, etc.) for display
 * @param n The number to format
 * @param p Precision (default: 3)
 * @param e Exponent precision (default: p-3)
 * @returns Formatted number string
 */
export const shortNum = function (n: string | number, p = 3, e = p - 3) {
  n = Number(n);
  if (n === 0) return '0';

  let ans;
  const absn = Math.abs(n);

  if (absn < Math.pow(10, -1 * p) || absn >= 10 ** 18) {
    ans = n.toExponential(Math.max(e, 0));
  } else if (absn < 1) {
    ans = n.toFixed(p);
  } else {
    const suffixes = ['', 'K', 'M', 'B', 'T', 'Q'];
    let index = Math.floor(Math.log10(absn) / 3);
    let scaled = n / 10 ** (index * 3);
    if (Math.round(scaled * 10 ** (p - 3)) == 10 ** p) {
      ++index;
      scaled = 1;
    }
    ans = scaled.toPrecision(p) + suffixes[index];
  }
  ans = ans.replace(/\.0+(\D|$)/, '$1');
  return ans.replace(/(\.\d*?)0+(\D|$)/, '$1$2');
};

/**
 * Default DOMPurify configuration for rich text content
 * Allows common formatting tags while maintaining security
 *
 * Based on common rich text formatting needs:
 * - Text formatting: strong, b, em, i, u
 * - Structure: p, br, h1-h6, blockquote
 * - Lists: ul, ol, li
 * - Links: a (with href attribute)
 *
 * KEEP_CONTENT: true preserves text content when removing disallowed tags
 * Note: Script tags and their content are completely removed for security
 */
export const RICH_TEXT_SANITIZE_CONFIG: Config = {
  ALLOWED_TAGS: [
    'p',
    'br',
    'strong',
    'b',
    'em',
    'i',
    'u',
    'a',
    'ul',
    'ol',
    'li',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'blockquote',
  ],
  ALLOWED_ATTR: ['href'],
  ALLOWED_URI_REGEXP:
    /^(?:(?:(?:f|ht)tps?|mailto):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
  KEEP_CONTENT: true, // Preserve text content when removing disallowed tags
};

const turndownService = new TurndownService({
  headingStyle: 'atx', // Use # for headings (GFM standard)
  hr: '---', // Use --- for horizontal rules (GFM standard)
  bulletListMarker: '*', // Use * for unordered lists (consistent with GFM)
  codeBlockStyle: 'fenced', // Use ``` for code blocks (GFM standard)
  fence: '```', // Use triple backticks for fenced code blocks
  emDelimiter: '*', // Use * for emphasis (GFM prefers this over _)
  strongDelimiter: '**', // Use ** for strong text (GFM standard)
  linkStyle: 'inlined', // Use [text](url) format (GFM standard)
  linkReferenceStyle: 'full', // Use full reference links when needed
  preformattedCode: false, // Don't convert <pre><code> to indented code blocks
  blankReplacement: function (_content, node) {
    // Handle void elements properly - check if it's a block-level element
    const blockElements = ['DIV', 'P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'BLOCKQUOTE', 'UL', 'OL', 'LI'];
    return blockElements.includes(node.nodeName) ? '\n\n' : '';
  },
  keepReplacement: function (content, node) {
    // Keep content for unknown elements
    const blockElements = ['DIV', 'P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'BLOCKQUOTE', 'UL', 'OL', 'LI'];
    return blockElements.includes(node.nodeName) ? '\n\n' + content + '\n\n' : content;
  },
  defaultReplacement: function (content, node) {
    // Default handling for unknown elements
    const blockElements = ['DIV', 'P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'BLOCKQUOTE', 'UL', 'OL', 'LI'];
    return blockElements.includes(node.nodeName) ? '\n\n' + content + '\n\n' : content;
  }
});

// Add a rule to remove the Lit comment if it's always present at the start
turndownService.addRule('litComment', {
  filter: (node) => {
    return node.nodeType === Node.COMMENT_NODE &&
           (node.nodeValue?.includes('lit$') ?? false);
  },
  replacement: () => '' // Remove the comment
});

// Add a rule to handle <br><br> patterns from contenteditable
turndownService.addRule('doubleBreak', {
  filter: (node) => {
    return (
      node.nodeName === 'BR' &&
      node.nextSibling !== null &&
      node.nextSibling.nodeName === 'BR'
    );
  },
  replacement: () => '\n\n' // Convert to proper paragraph break
});

// Add support for strikethrough (GFM extension)
turndownService.addRule('strikethrough', {
  filter: ['del', 's'],
  replacement: (content) => `~~${content}~~`
});

// Add support for task lists (GFM extension)
turndownService.addRule('taskList', {
  filter: (node) => {
    return (
      node.nodeName === 'INPUT' &&
      node.getAttribute('type') === 'checkbox' &&
      node.parentNode !== null &&
      node.parentNode.nodeName === 'LI'
    );
  },
  replacement: (_content, node) => {
    const inputElement = node as HTMLInputElement;
    return inputElement.checked ? '[x] ' : '[ ] ';
  }
});

// Add support for tables (basic GFM table support)
turndownService.addRule('table', {
  filter: 'table',
  replacement: (content) => {
    // Basic table conversion - this is simplified
    // For full table support, you'd need more complex logic
    return '\n\n' + content + '\n\n';
  }
});

// Add support for table rows and cells
turndownService.addRule('tableRow', {
  filter: 'tr',
  replacement: (content, _node) => {
    const cells = content.trim();
    return cells ? `| ${cells} |\n` : '';
  }
});

turndownService.addRule('tableCell', {
  filter: ['td', 'th'],
  replacement: (content) => {
    return content.trim() + ' | ';
  }
});

/**
 * Sanitizes HTML and markdown content using DOMPurify with rich text configuration
 * and returns a Lit directive that safely renders the HTML in templates
 *
 * @param content - The content to process (HTML or markdown)
 * @returns Lit directive that renders sanitized HTML safely
 *
 * @example
 * ```typescript
 * // HTML input
 * const htmlInput = '<p>Hello <script>alert("xss")</script> <strong>world</strong>!</p>';
 * const safeHtml = formattedText(htmlInput);
 * // Use in Lit template: html`<div>${safeHtml}</div>`
 * // Result: <div><p>Hello  <strong>world</strong>!</p></div>
 *
 * // Markdown input
 * const markdownInput = '# Hello\n\nThis is **bold** and *italic* text.';
 * const safeMarkdown = formattedText(markdownInput);
 * // Use in Lit template: html`<div>${safeMarkdown}</div>`
 * // Result: <div><h1>Hello</h1><p>This is <strong>bold</strong> and <em>italic</em> text.</p></div>
 * ```
 */
export function formattedText(content: string): DirectiveResult {
  const markdownInput = turndownService.turndown(content);
  const htmlContent = marked(markdownInput) as string;
  const sanitized = DOMPurify.sanitize(htmlContent, RICH_TEXT_SANITIZE_CONFIG);
  return unsafeHTML(sanitized);
}
