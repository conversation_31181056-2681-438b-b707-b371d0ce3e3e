import { LitElement, css, html } from 'lit';
import { customElement, state, query } from 'lit/decorators.js';
import { formattedText } from '@utils/format-utils';

/**
 * Test component that simulates the actual contenteditable process
 * This puts markdown into a contenteditable div, reads it back out,
 * and shows what our formattedText function produces
 */
@customElement('contenteditable-test')
export class ContentEditableTest extends LitElement {
  static styles = css`
    :host {
      display: block;
      padding: 1rem;
      max-width: 1000px;
      margin: 0 auto;
    }

    .test-section {
      margin-bottom: 2rem;
      padding: 1rem;
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: 4px;
    }

    .contenteditable {
      border: 2px solid var(--sl-color-primary-300);
      padding: 1rem;
      min-height: 200px;
      background: white;
      font-family: monospace;
      white-space: pre-wrap;
      margin: 1rem 0;
    }

    .output-section {
      margin-top: 1rem;
    }

    .raw-output {
      background: var(--sl-color-neutral-100);
      padding: 0.5rem;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.9em;
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
      margin: 0.5rem 0;
    }

    .rendered-output {
      border: 1px solid var(--sl-color-neutral-200);
      padding: 1rem;
      border-radius: 4px;
      background: white;
      margin: 0.5rem 0;
    }

    button {
      background: var(--sl-color-primary-600);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      margin: 0.5rem 0.5rem 0.5rem 0;
    }

    button:hover {
      background: var(--sl-color-primary-700);
    }
  `;

  @query('#contenteditable') contentEditableDiv!: HTMLDivElement;

  @state() private rawContent = '';

  private originalMarkdown = `# Can we do markdown?

* Yes
* we
* can

I **love** *spaghetti*

## code

\`\`\`typescript
function hello() {
  console.log('hello');
}
\`\`\`

> Dorothy followed her through many of the beautiful rooms in her castle.

1. First item
2. Second item
3. Third item
    - Indented item
    - Indented item
4. Fourth item

[![GitBook](https://img.shields.io/static/v1?message=Guide%20on%20GitBook&logo=gitbook&logoColor=fff&label=%20&labelColor=0A4364&color=0E6A9C)](https://guide.updraft.fund/)
# 🪁Updraft
![updraft-512](https://github.com/UpdraftFund/.github/raw/main/profile/updraft-512.png)

* [Updraft App](https://www.updraft.fund)
* [Updraft Discord](https://discord.gg/mQJ58MY6Nz)
* [Updraft Guide](https://guide.updraft.fund)
* [Updraft Blog](https://mirror.xyz/0xB7C5583C4C81e97e2883F4B2A250368d8eEcB0e2). [Read the introductory post on Updraft](https://mirror.xyz/0xB7C5583C4C81e97e2883F4B2A250368d8eEcB0e2/T7Nov-X_ckpJG0MJAnhpF-aYShz7Ey26-tSqakeKEQw).
* [Updraft for orgs deck](https://docs.google.com/presentation/d/19Yz_sKd_6erlpmpTcYp-VpZvQBPd8HBOPfANqiEubeY)
* [Updraft overview deck](https://docs.google.com/presentation/d/1opRgIcf7iH_aiQzKpeVr110kt5L8bA6mtbjl2YGPXAU)

---

<https://www.markdownguide.org>
<<EMAIL>>`;

  private loadMarkdown() {
    this.contentEditableDiv.textContent = this.originalMarkdown;
  }

  private readContent() {
    this.rawContent = this.contentEditableDiv.innerHTML;
    console.log('Raw HTML from contenteditable:', this.rawContent);
  }

  private clearContent() {
    this.contentEditableDiv.innerHTML = '';
    this.rawContent = '';
  }

  render() {
    return html`
      <h1>ContentEditable Test</h1>
      <p>This test simulates the actual process: markdown → contenteditable → read back → formattedText()</p>
      
      <div class="test-section">
        <h2>Step 1: ContentEditable Div</h2>
        <p>This div is contenteditable. You can type in it or load the test markdown.</p>
        
        <div 
          id="contenteditable"
          class="contenteditable" 
          contenteditable="true"
          placeholder="Type markdown here or click 'Load Test Markdown'"
        ></div>
        
        <button @click=${this.loadMarkdown}>Load Test Markdown</button>
        <button @click=${this.readContent}>Read Content & Process</button>
        <button @click=${this.clearContent}>Clear</button>
      </div>

      ${this.rawContent ? html`
        <div class="test-section">
          <h2>Step 2: Raw HTML from ContentEditable</h2>
          <p>This is what we get when we read <code>innerHTML</code> from the contenteditable div:</p>
          <div class="raw-output">${this.rawContent}</div>
        </div>
      ` : ''}

      ${this.rawContent ? html`
        <div class="test-section">
          <h2>Step 3: Processed through formattedText()</h2>
          <p>This is what our formattedText() function produces when given the raw HTML above:</p>

          <h3>Rendered Result:</h3>
          <div class="rendered-output">
            ${formattedText(this.rawContent)}
          </div>
        </div>
      ` : ''}
    `;
  }
}
