import { LitElement, css, html } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { formattedText } from '@utils/format-utils';

/**
 * Demo component to test markdown functionality
 * This can be used to verify that the formattedText function
 * properly handles both markdown and HTML input
 */
@customElement('markdown-demo')
export class MarkdownDemo extends LitElement {
  static styles = css`
    :host {
      display: block;
      padding: 1rem;
      max-width: 800px;
      margin: 0 auto;
    }

    .demo-section {
      margin-bottom: 2rem;
      padding: 1rem;
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: 4px;
    }

    .demo-title {
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: var(--sl-color-primary-600);
    }

    .input {
      background: var(--sl-color-neutral-100);
      padding: 0.5rem;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.9em;
      margin-bottom: 0.5rem;
      white-space: pre-wrap;
    }

    .output {
      border: 1px solid var(--sl-color-neutral-200);
      padding: 0.5rem;
      border-radius: 4px;
      background: white;
    }
  `;

  @state() private testCases = [
    {
      title: 'Pure Markdown',
      input: '# Hello World\n\nThis is **bold** and *italic* text.\n\n- Item 1\n- Item 2\n- Item 3',
    },
    {
      title: 'Mangled HTML from contenteditable (your example)',
      input: '<!--?lit$347893344$--># Can we do markdown?<br><br>* Yes<br>* we<br>* can<br><br>I **love** *spaghetti*<br><br>## code<br><br>```typescript<br>function hello() {<br>&nbsp; console.log(\'hello\');<br>}<br>```<br><br>&gt; Dorothy followed her through many of the beautiful rooms in her castle.<br><br>1. First item<br>2. Second item<br>3. Third item<br>&nbsp; &nbsp; - Indented item<br>&nbsp; &nbsp; - Indented item<br>4. Fourth item',
    },
    {
      title: 'More contenteditable mangling',
      input: '<!--?lit$123456789$-->## This is a header<br>With some **bold text**<br><br>And a list:<br>* Item one<br>* Item two<br>* Item three',
    },
    {
      title: 'Mixed HTML and Markdown (should pass through)',
      input: '<p>This HTML stays as HTML</p>\n\n## But this markdown gets converted\n\nWith a [link](https://example.com)',
    },
    {
      title: 'GFM Features',
      input: '~~This text is crossed out~~\n\n| Name | Age |\n|------|-----|\n| John | 25 |\n| Jane | 30 |',
    },
    {
      title: 'Code and Blockquotes',
      input: '> This is a blockquote\n\nHere is `inline code` and:\n\n```javascript\nconsole.log("Hello World!");\n```',
    },
    {
      title: 'Plain Text',
      input: 'Just plain text with no special formatting.',
    },
  ];

  render() {
    return html`
      <h1>Markdown Demo</h1>
      <p>This demo shows how the <code>formattedText()</code> function handles different types of input.</p>
      
      ${this.testCases.map(
        (testCase) => html`
          <div class="demo-section">
            <div class="demo-title">${testCase.title}</div>
            <div class="input">Input: ${testCase.input}</div>
            <div class="output">
              <strong>Output:</strong>
              ${formattedText(testCase.input)}
            </div>
          </div>
        `
      )}
    `;
  }
}
