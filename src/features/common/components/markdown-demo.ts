import { LitElement, css, html } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { formattedText } from '@utils/format-utils';

/**
 * Demo component to test markdown functionality
 * This can be used to verify that the formattedText function
 * properly handles both markdown and HTML input
 */
@customElement('markdown-demo')
export class MarkdownDemo extends LitElement {
  static styles = css`
    :host {
      display: block;
      padding: 1rem;
      max-width: 800px;
      margin: 0 auto;
    }

    .demo-section {
      margin-bottom: 2rem;
      padding: 1rem;
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: 4px;
    }

    .demo-title {
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: var(--sl-color-primary-600);
    }

    .input {
      background: var(--sl-color-neutral-100);
      padding: 0.5rem;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.9em;
      margin-bottom: 0.5rem;
      white-space: pre-wrap;
    }

    .output {
      border: 1px solid var(--sl-color-neutral-200);
      padding: 0.5rem;
      border-radius: 4px;
      background: white;
    }
  `;

  @state() private testCases = [
    {
      title: 'Pure Markdown',
      input: '# Hello World\n\nThis is **bold** and *italic* text.\n\n- Item 1\n- Item 2\n- Item 3',
    },
    {
      title: 'Mangled HTML from contenteditable (like your example)',
      input: '<!--?lit$225644267$--># Can we do markdown<br><br>* yes<br>* we<br>* can<br><br>&lt;https://www.markdownguide.org&gt;&lt;<EMAIL>&gt;',
    },
    {
      title: 'GFM Features - Strikethrough and Tables',
      input: '~~This text is crossed out~~\n\n| Name | Age | City |\n|------|-----|------|\n| John | 25 | NYC |\n| Jane | 30 | LA |',
    },
    {
      title: 'Mixed HTML and Markdown',
      input: '<p>This is HTML with <strong>bold</strong> text</p>\n\n## And a markdown header\n\nWith a [link](https://example.com)',
    },
    {
      title: 'Code Blocks and Syntax Highlighting',
      input: 'Here is some `inline code` and:\n\n```javascript\nfunction hello() {\n  console.log("Hello World!");\n}\n```\n\nAnd some Python:\n\n```python\ndef greet(name):\n    print(f"Hello, {name}!")\n```',
    },
    {
      title: 'Blockquotes and Lists',
      input: '> This is a blockquote\n> with multiple lines\n\n1. First ordered item\n2. Second ordered item\n   - Nested unordered item\n   - Another nested item\n3. Third ordered item',
    },
    {
      title: 'Plain Text',
      input: 'Just plain text with no special formatting.',
    },
  ];

  render() {
    return html`
      <h1>Markdown Demo</h1>
      <p>This demo shows how the <code>formattedText()</code> function handles different types of input.</p>
      
      ${this.testCases.map(
        (testCase) => html`
          <div class="demo-section">
            <div class="demo-title">${testCase.title}</div>
            <div class="input">Input: ${testCase.input}</div>
            <div class="output">
              <strong>Output:</strong>
              ${formattedText(testCase.input)}
            </div>
          </div>
        `
      )}
    `;
  }
}
