import { LitElement, css, html } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { formattedText } from '@utils/format-utils';

// Import turndown for debugging
import TurndownService from 'turndown';

const debugTurndownService = new TurndownService({
  headingStyle: 'atx',
  bulletListMarker: '*',
  codeBlockStyle: 'fenced',
  fence: '```',
  emDelimiter: '*',
  strongDelimiter: '**',
});

// Add the same rules as our main service
debugTurndownService.addRule('removeComments', {
  filter: (node) => node.nodeType === Node.COMMENT_NODE,
  replacement: () => ''
});

debugTurndownService.addRule('lineBreaks', {
  filter: 'br',
  replacement: () => '\n'
});

debugTurndownService.addRule('nonBreakingSpaces', {
  filter: (node) => {
    return node.nodeType === Node.TEXT_NODE &&
           node.textContent !== null &&
           node.textContent.includes('\u00A0');
  },
  replacement: (content) => content.replace(/\u00A0/g, ' ')
});

function debugTurndown(content: string): string {
  console.log('Original content:', content);
  const result = debugTurndownService.turndown(content);
  console.log('Turndown result:', result);
  return result;
}

/**
 * Demo component to test markdown functionality
 * This can be used to verify that the formattedText function
 * properly handles both markdown and HTML input
 */
@customElement('markdown-demo')
export class MarkdownDemo extends LitElement {
  static styles = css`
    :host {
      display: block;
      padding: 1rem;
      max-width: 800px;
      margin: 0 auto;
    }

    .demo-section {
      margin-bottom: 2rem;
      padding: 1rem;
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: 4px;
    }

    .demo-title {
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: var(--sl-color-primary-600);
    }

    .input {
      background: var(--sl-color-neutral-100);
      padding: 0.5rem;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.9em;
      margin-bottom: 0.5rem;
      white-space: pre-wrap;
    }

    .output {
      border: 1px solid var(--sl-color-neutral-200);
      padding: 0.5rem;
      border-radius: 4px;
      background: white;
    }
  `;

  @state() private testCases = [
    {
      title: 'Debug: Simple comment test',
      input: '<!--?lit$123$-->Hello World',
    },
    {
      title: 'Your original markdown (clean)',
      input: `# Can we do markdown?

* Yes
* we
* can

I **love** *spaghetti*

## code

\`\`\`typescript
function hello() {
  console.log('hello');
}
\`\`\`

> Dorothy followed her through many of the beautiful rooms in her castle.

1. First item
2. Second item
3. Third item
    - Indented item
    - Indented item
4. Fourth item`,
    },
    {
      title: 'Simulated contenteditable output',
      input: '<!--?lit$703317988$--># Can we do markdown?<br><br>* Yes<br>* we<br>* can<br><br>I **love** *spaghetti*<br><br>## code<br><br>```typescript<br>function hello() {<br>&nbsp; console.log(\'hello\');<br>}<br>```<br><br>&gt; Dorothy followed her through many of the beautiful rooms in her castle.<br><br>1. First item<br>2. Second item<br>3. Third item<br>&nbsp; &nbsp; - Indented item<br>&nbsp; &nbsp; - Indented item<br>4. Fourth item',
    },
  ];

  render() {
    return html`
      <h1>Markdown Demo</h1>
      <p>This demo shows how the <code>formattedText()</code> function handles different types of input.</p>
      
      ${this.testCases.map(
        (testCase, index) => html`
          <div class="demo-section">
            <div class="demo-title">${testCase.title}</div>
            <div class="input">Input: ${testCase.input}</div>
            ${index === 0 ? html`
              <div class="input">
                <strong>Debug turndown:</strong> ${debugTurndown(testCase.input)}
              </div>
            ` : ''}
            <div class="output">
              <strong>Output:</strong>
              ${formattedText(testCase.input)}
            </div>
          </div>
        `
      )}
    `;
  }
}
