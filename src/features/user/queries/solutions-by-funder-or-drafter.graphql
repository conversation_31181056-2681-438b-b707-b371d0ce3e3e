query SolutionsByFunderOrDrafter($user: String!, $first: Int = 5, $skip: Int = 0) {
    # Get solutions where user is a funder
    fundedSolutions: solutionContributions(
        first: $first,
        skip: $skip,
        where: { funder: $user }
    ) {
        solution {
            ...SolutionFields
        }
        createdTime
    }

    # Get solutions where user is a drafter
    draftedSolutions: solutions(
        first: $first,
        skip: $skip,
        where: { drafter: $user }
    ) {
        ...SolutionFields
    }
}

