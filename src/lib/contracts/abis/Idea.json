[{"inputs": [{"internalType": "uint256", "name": "contributorFee_", "type": "uint256"}, {"internalType": "address", "name": "humanity_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "CannotAirdropInFirstCycle", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "contribution", "type": "uint256"}, {"internalType": "uint256", "name": "minFee", "type": "uint256"}], "name": "ContributionLessThanMinFee", "type": "error"}, {"inputs": [], "name": "ContributorFeeOverOneHundredPercent", "type": "error"}, {"inputs": [], "name": "NotOnlyPosition", "type": "error"}, {"inputs": [], "name": "PositionDoesNotExist", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "positionAmount", "type": "uint256"}], "name": "SplitAmountMoreThanPosition", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalShares", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokens", "type": "uint256"}], "name": "Contributed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "senderPositionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "recipientPositionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "contribution", "type": "uint256"}], "name": "PositionTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "originalPositionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "numNewPositions", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "firstNewPositionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "contributionPerNewPosition", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "contributionLeftInOriginal", "type": "uint256"}], "name": "Split", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalShares", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokens", "type": "uint256"}], "name": "Withdrew", "type": "event"}, {"inputs": [], "name": "accrualRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "airdrop", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "name": "checkPosition", "outputs": [{"internalType": "uint256", "name": "positionTokens", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "checkPosition", "outputs": [{"internalType": "uint256", "name": "positionTokens", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "contribute", "outputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "contributorFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "contributorFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "crowdFund", "outputs": [{"internalType": "contract ICrowdFund", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentCycleNumber", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "cycle<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "cycles", "outputs": [{"internalType": "uint256", "name": "number", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "uint256", "name": "fees", "type": "uint256"}, {"internalType": "bool", "name": "hasContributions", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "humanity", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "numPositions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_cycleNumber", "type": "uint256"}, {"internalType": "uint256", "name": "_tokens", "type": "uint256"}], "name": "pendingShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "percentFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "percentScale", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "startCycleIndex", "type": "uint256"}, {"internalType": "uint256", "name": "tokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"internalType": "uint256", "name": "numSplits", "type": "uint256"}], "name": "split", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"internalType": "uint256", "name": "numSplits", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "split", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "startTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}], "name": "transferPosition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "name": "transferPosition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256[]", "name": "positionIndexes", "type": "uint256[]"}], "name": "transferPositions", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]