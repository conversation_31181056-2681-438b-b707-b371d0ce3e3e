[{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "contract IERC20", "name": "fundingToken_", "type": "address"}, {"internalType": "contract IERC20", "name": "stakingToken_", "type": "address"}, {"internalType": "uint256", "name": "goal", "type": "uint256"}, {"internalType": "uint256", "name": "deadline_", "type": "uint256"}, {"internalType": "uint256", "name": "contributorFee_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AlreadyRefunded", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "contributedTime", "type": "uint256"}, {"internalType": "uint256", "name": "goalExtendedTime", "type": "uint256"}], "name": "ContributedBeforeGoalExtended", "type": "error"}, {"inputs": [], "name": "ContributorFeeOverOneHundredPercent", "type": "error"}, {"inputs": [], "name": "GoalFailed", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "oldGoal", "type": "uint256"}, {"internalType": "uint256", "name": "newGoal", "type": "uint256"}], "name": "GoalMustIncrease", "type": "error"}, {"inputs": [], "name": "GoalNotFailed", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "contributed", "type": "uint256"}, {"internalType": "uint256", "name": "goal", "type": "uint256"}], "name": "GoalNotReached", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "MustSetDeadlineInFuture", "type": "error"}, {"inputs": [], "name": "NotOnlyPosition", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "PositionDoesNotExist", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "positionAmount", "type": "uint256"}], "name": "SplitAmountMoreThanPosition", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "available", "type": "uint256"}], "name": "WithdrawMoreThanAvailable", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalShares", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalContributed", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokens", "type": "uint256"}], "name": "Contributed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FeesCollected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokensLeft", "type": "uint256"}], "name": "FundsWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "goal", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "GoalExtended", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "senderPositionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "recipientPositionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "contribution", "type": "uint256"}], "name": "PositionTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "stakeCollected", "type": "uint256"}], "name": "Refunded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "SolutionUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "originalPositionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "numNewPositions", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "firstNewPositionIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "contributionPerNewPosition", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "contributionLeftInOriginal", "type": "uint256"}], "name": "Split", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "sent", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newStake", "type": "uint256"}], "name": "StakeTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "stake", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalStake", "type": "uint256"}], "name": "StakeUpdated", "type": "event"}, {"inputs": [], "name": "accrualRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "addStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "name": "checkPosition", "outputs": [{"internalType": "uint256", "name": "feesEarned", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "checkPosition", "outputs": [{"internalType": "uint256", "name": "feesEarned", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "name": "collectFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "collectFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "contribute", "outputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "contributorFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "crowdFund", "outputs": [{"internalType": "contract ICrowdFund", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentCycleNumber", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "cycle<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "cycleNumberAtTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "cycles", "outputs": [{"internalType": "uint256", "name": "number", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "uint256", "name": "fees", "type": "uint256"}, {"internalType": "bool", "name": "hasContributions", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deadline", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "goal", "type": "uint256"}], "name": "extendGoal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "goal", "type": "uint256"}, {"internalType": "uint256", "name": "deadline_", "type": "uint256"}, {"internalType": "bytes", "name": "solutionData", "type": "bytes"}], "name": "extendGoal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "goal", "type": "uint256"}, {"internalType": "uint256", "name": "deadline_", "type": "uint256"}], "name": "extendGoal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "fundingGoal", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "fundingToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "goalExtendedTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "goalFailed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "numPositions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_cycleNumber", "type": "uint256"}, {"internalType": "uint256", "name": "_tokens", "type": "uint256"}], "name": "pendingShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "percentScale", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "contribution", "type": "uint256"}, {"internalType": "uint256", "name": "contributionTime", "type": "uint256"}, {"internalType": "uint256", "name": "startCycleIndex", "type": "uint256"}, {"internalType": "uint256", "name": "lastCollectedCycleIndex", "type": "uint256"}, {"internalType": "bool", "name": "refunded", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "name": "refund", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "refund", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "removeStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"internalType": "uint256", "name": "numSplits", "type": "uint256"}], "name": "split", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "positionIndex", "type": "uint256"}, {"internalType": "uint256", "name": "numSplits", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "split", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stake", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "stakes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakingToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "startTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokensContributed", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokensWithdrawn", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}], "name": "transferPosition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "positionIndex", "type": "uint256"}], "name": "transferPosition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256[]", "name": "positionIndexes", "type": "uint256[]"}], "name": "transferPositions", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "transferStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "updateSolution", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawFunds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawFunds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]