{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"name": {"type": "string"}, "team": {"type": "string"}, "links": {"type": "array", "items": {"type": "string"}}, "image": {"description": "data URL of embedded image", "type": "string"}, "about": {"type": "string"}, "news": {"description": "recent updates from the profile owner", "type": "string"}}, "anyOf": [{"required": ["name"]}, {"required": ["team"]}]}